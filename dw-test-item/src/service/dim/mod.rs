use crate::config::DwTestItemConfig;
use crate::cpu_limit::get_current_thread_count;
use arrow::array::RecordBatch;
use ck_provider::CkProviderError::ExecutionError;
use ck_provider::{AsyncCk<PERSON>hannel, AsyncCkSender, CkConfig, CkProviderError, CkProviderImpl, CkStreamProcessorBuilder, GenericStreamProcessor, GenericStreamProcessorBuilder, StreamConfig};
use clickhouse::Row;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::{CkSink, SinkHandler};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::model::constant::{CUSTOMER, DIM_TEST_PROGRAM_PARTITION_TEMPLATE, FACTORY, TEST_AREA};
use common::model::key::WaferKey;
use common::parquet::RecordBatchWrapper;
use log::info;
use parquet_provider::hdfs_provider::{HdfsConfig, HdfsProvider};
use parquet_provider::parquet_provider::{write_parquet_multi, ParquetProviderError, TempDirCleanup};
use parquet_provider::RecordBatchStreamWriter;
use rayon::prelude::*;
use serde::Serialize;
use std::collections::HashMap;
use std::error::Error;
use std::fs;
use std::future::Future;
use std::sync::Arc;
use std::time::Duration;
use tokio::task::JoinHandle;
use uuid::Uuid;

pub mod cp_dim_test_item_service;
pub mod ft_dim_test_item_service;
pub mod test_item_service;
pub mod test_program_test_item_service;

async fn write_to_ck_by_partition<T>(
    config: &DwTestItemConfig,
    data: &[T],
    handler: impl SinkHandler + Send + Sync,
    partition: &str,
) -> Result<(), Box<dyn Error + Send + Sync>>
where
    T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
{
    use common::ck::ck_sink::CkSink;

    if data.is_empty() {
        log::info!("No data to write.");
        return Ok(());
    }

    log::info!("Writing {} records to ClickHouse.", data.len());

    let ck_config = config.get_ck_config(handler.db_name());

    CkSink::write_to_ck_with_partition_from_config(data, &config.dim_num_partitions, ck_config, &handler, partition)
        .await
        .map_err(|e| -> Box<dyn Error + Send + Sync> {
            log::error!("写入clickhouse 失败: {}, 数据量为: {}", handler.table_name(), &data.len());
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
        })?;

    log::info!("Successfully wrote {} records to ClickHouse.", data.len());
    Ok(())
}

pub async fn write_to_clickhouse_after_tombstone<T>(
    handler: impl SinkHandler + Send + Sync,
    config: &DwTestItemConfig,
    data: &[T],
    wafer_key: &WaferKey,
) -> color_eyre::Result<()>
where
    T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
{
    tombstone(&handler, config, &wafer_key, None).await?;
    CkSink::write_to_ck(
        data,
        config.dim_num_partitions.parse::<usize>()?,
        &config.get_ck_config(handler.db_name()),
        &handler,
        false,
    )
    .await
    .map_err(|e| {
        log::error!("写入clickhouse失败: {}", e);
        let err_str = e.to_string();
        ExecutionError(err_str)
    })?;
    Ok(())
}

pub async fn tombstone(
    handler: &(impl SinkHandler + Send + Sync),
    config: &DwTestItemConfig,
    wafer_key: &WaferKey,
    lot_bucket: Option<i32>,
) -> color_eyre::Result<()> {
    let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());
    CkOperate::tombstone_ck(
        &wafer_key.customer,
        &wafer_key.factory,
        &wafer_key.test_area,
        &wafer_key.lot_id,
        &wafer_key.lot_type,
        &wafer_key.test_stage,
        &wafer_key.device_id,
        lot_bucket,
        &wafer_key.wafer_no,
        &table_full_name,
        &config.pick_random_ck_node_host(),
        &config.get_ck_address_list(),
        &config.ck_username,
        &config.ck_password,
        handler.partition_expr(),
        Some(chrono::Utc::now()),
    )
    .await
    .map_err(|e| {
        let err_str = e.to_string();
        log::error!("执行tombstone操作失败: {}", e);
        crate::error::DatawareError::TombstoneFailed(err_str)
    })?;

    Ok(())
}

pub fn get_test_program_partition(customer: &str, test_area: &str, factory: &str) -> String {
    DIM_TEST_PROGRAM_PARTITION_TEMPLATE
        .replace(CUSTOMER, customer)
        .replace(TEST_AREA, test_area)
        .replace(FACTORY, factory)
}

/// Write DimTestProgramTestItem data to HDFS using Parquet format
async fn write_to_hdfs<T>(
    table_path: &str,
    data: &[T],
    batch_size: usize,
    hdfs_config: &HdfsConfig,
) -> Result<(), Box<dyn Error + Send + Sync>>
where
    T: RecordBatchWrapper + Send + Sync + Clone + 'static,
{
    if data.is_empty() {
        log::warn!("No data to write to HDFS");
        return Ok(());
    }

    log::info!("写入parquet文件到路径: {}", table_path);

    // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
    let data_vec = data.to_vec();
    let data_batches = vec![data_vec];
    let data_refs = data_batches.iter().collect();
    write_parquet_multi(
        &table_path,
        &data_refs,
        Some(hdfs_config),
        batch_size, // batch_size
    )
    .await
    .map_err(|e| {
        Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
            as Box<dyn Error + Send + Sync>
    })?;
    log::info!("成功写入parquet文件到路径: {}", table_path);

    Ok(())
}

pub async fn write_to_clickhouse_concurrent<T: Sync + Send + Serialize + Row + 'static>(
    handler: &(impl SinkHandler + Send + Sync),
    properties: DwTestItemConfig,
) -> Result<(AsyncCkSender<T>, JoinHandle<()>), Box<dyn Error + Send + Sync>> {
    let ck_config = properties.get_ck_config(properties.dwd_db_name.as_str());

    let db_table_name = format!("{}.{}", handler.db_name(), handler.table_name());
    let batch_size = properties.get_batch_size()?;

    let optimal_concurrent_flushes = get_current_thread_count();
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<T>(stream_config.clone(), 10);

    let ck_provider = CkProviderImpl::new(ck_config.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    Ok((sender, processor_handle))
}

pub struct WriteHandle<T>
where
    T: Sync + Send + Serialize + Row + 'static,
{
    ck_sender: AsyncCkSender<T>,
    parquet_sender: AsyncCkSender<RecordBatch>,
    ck_handle: JoinHandle<()>,
    parquet_processor: GenericStreamProcessor<RecordBatch, RecordBatchStreamWriter>,
    file_path: String,
    temp_file_path: String,
    hdfs_config: Option<HdfsConfig>,
    temp_dir_cleanup: TempDirCleanup,
}

impl<T> WriteHandle<T>
where
    T: Sync + Send + Serialize + Row + 'static,
{
    pub async fn apply(
        self,
        task: JoinHandle<Result<(), Box<dyn Error + Send + Sync>>>,
    ) -> Result<(), CkProviderError> {
        let send_handle = tokio::spawn(async move {
            // 等待所有生产者任务完成

            if let Err(e) = task.await {
                log::error!("Task join error: {}", e);
            }

            // 发送结束信号到两个流
            if let Err(e) = self.ck_sender.send(None).await {
                log::error!("Failed to send CK end signal: {}", e)
            };

            if let Err(e) = self.parquet_sender.send(None).await {
                log::error!("Failed to send Parquet end signal: {}", e)
            };
        });
        self.parquet_processor.start().await?;

        send_handle
            .await
            .map_err(|e| ParquetProviderError::RuntimeError(format!("Send handle error: {}", e)))?;

        self.ck_handle.await.map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
        info!("Successfully completed concurrent writing to ClickHouse and Parquet temp file: {}", self.temp_file_path);

        // 如果有HDFS配置，上传文件
        if let Some(hdfs_config) = self.hdfs_config {
            let hdfs_provider =
                HdfsProvider::new(hdfs_config.clone()).map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
            hdfs_provider
                .upload(&self.temp_file_path, self.file_path.as_str())
                .await
                .map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
            info!("Successfully uploaded parquet file to HDFS: {}", self.file_path);
        }
        drop(self.temp_dir_cleanup);
        Ok(())
    }
}

pub async fn write_to_clickhouse_and_parquet_concurrent<T: Sync + Send + Serialize + Row + 'static>(
    handler: &(impl SinkHandler + Send + Sync),
    file_path: &str,
    config: &DwTestItemConfig,
    ck_config: CkConfig,
    hdfs_config: Option<&HdfsConfig>,
) -> Result<WriteHandle<T>, Box<dyn Error + Send + Sync>> {
    let db_table_name = format!("{}.{}", handler.db_name(), handler.table_name());
    let batch_size = config.get_batch_size()?;

    // 如果有HDFS配置，先删除目标文件
    if let Some(hdfs_config) = hdfs_config {
        let hdfs_provider = HdfsProvider::new(hdfs_config.clone())?;
        hdfs_provider.delete(file_path).await?;
        info!("Successfully deleted HDFS file: {}", file_path);
    }

    let optimal_concurrent_flushes = get_current_thread_count();
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let ck_stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // Parquet流配置 - 串行写入优化
    let parquet_stream_config = StreamConfig::default()
        .with_buffer_size(5)
        .with_batch_size(1)
        .with_flush_interval(Duration::from_millis(100))
        .with_max_retries(0)
        .with_parallel_flush(false)
        .with_max_concurrent_flushes(1);

    // 创建两个独立的流式通道
    let (ck_sender, ck_receiver) = AsyncCkChannel::new::<T>(ck_stream_config.clone(), 10);
    let (parquet_sender, parquet_receiver) = AsyncCkChannel::new::<RecordBatch>(parquet_stream_config.clone(), 2);

    // 创建ClickHouse流处理器
    let ck_provider = CkProviderImpl::new(ck_config.clone());
    let ck_processor = CkStreamProcessorBuilder::new()
        .with_receiver(ck_receiver)
        .with_provider(ck_provider.clone())
        .with_config(ck_stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 准备Parquet临时文件
    let temp_uuid = Uuid::new_v4().to_string();
    let base_path = hdfs_config.map(|c| c.local_path_prefix.as_str()).unwrap_or("/tmp");
    let temp_dir = format!("{}/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", base_path, temp_uuid);
    info!("write_test_item_detail_concurrent to tmp dir: {}", temp_dir);

    let temp_dir_cleanup = TempDirCleanup::new(&temp_dir);
    fs::create_dir_all(&temp_dir).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
    })?;

    let file_name = "all.parquet";
    let temp_file_path = format!("{}/{}", temp_dir, file_name);

    // 创建Parquet流处理器
    let parquet_writer = RecordBatchStreamWriter::new(temp_file_path.clone())?;
    let parquet_processor = GenericStreamProcessorBuilder::new()
        .with_receiver(parquet_receiver)
        .with_writer(parquet_writer)
        .with_config(parquet_stream_config)
        .build()
        .map_err(|e| ParquetProviderError::RuntimeError(format!("Failed to build parquet stream processor: {}", e)))?;

    // 启动ClickHouse流处理器
    let ck_processor_handle: JoinHandle<()> = tokio::spawn(async move {
        if let Err(e) = ck_processor.start().await {
            eprintln!("ClickHouse流处理器错误: {:?}", e);
        }
    });


    Ok(WriteHandle {
        ck_sender,
        parquet_sender,
        ck_handle: ck_processor_handle,
        parquet_processor,
        file_path: file_path.to_string(),
        temp_file_path,
        hdfs_config: hdfs_config.map(|c| c.clone()),
        temp_dir_cleanup,
    })
}
